# GreenMilesEV

A full-stack electric vehicle management platform built with modern web and mobile technologies.

## 🚗 Project Overview

GreenMilesEV is a comprehensive platform for electric vehicle management, featuring:
- **Web Application**: Next.js with TypeScript, Tailwind CSS, and shadcn/ui
- **Mobile Application**: React Native with Expo
- **Backend**: Supabase for authentication, database, and API
- **Shared Code**: Common types and utilities across platforms

## 📁 Project Structure

```
greenmiles-ev/
├── apps/
│   ├── web/          # Next.js web application
│   └── mobile/       # React Native mobile app
├── packages/
│   └── shared/       # Shared types and utilities
├── docs/             # Documentation
└── package.json      # Root package.json with workspace configuration
```

## 🚀 Quick Start

### Prerequisites

- Node.js 18+ and npm 9+
- Git
- For mobile development: Expo CLI

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd greenmiles-ev
```

2. Install dependencies for all projects:
```bash
npm run install:all
```

3. Set up environment variables (see individual app READMEs)

4. Start development servers:
```bash
# Start both web and mobile
npm run dev

# Or start individually
npm run dev:web
npm run dev:mobile
```

## 🛠️ Development

### Available Scripts

- `npm run dev` - Start both web and mobile development servers
- `npm run build` - Build both applications
- `npm run lint` - Run linting for all projects
- `npm run type-check` - Run TypeScript type checking
- `npm run clean` - Clean all node_modules

### Individual Applications

- **Web App**: See `apps/web/README.md`
- **Mobile App**: See `apps/mobile/README.md`
- **Shared Packages**: See `packages/shared/README.md`

## 🏗️ Tech Stack

### Frontend
- **Web**: Next.js 14+, TypeScript, Tailwind CSS, shadcn/ui
- **Mobile**: React Native, Expo, TypeScript

### Backend
- **Database**: Supabase (PostgreSQL)
- **Authentication**: Supabase Auth
- **API**: Supabase REST API & Real-time subscriptions

### Development Tools
- TypeScript for type safety
- ESLint & Prettier for code quality
- Husky for git hooks
- Conventional commits

## 📱 Features

- User authentication and profiles
- Electric vehicle management
- Charging station locator
- Trip planning and tracking
- Energy consumption analytics
- Real-time notifications

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Run tests and linting
5. Submit a pull request

## 📄 License

MIT License - see LICENSE file for details
