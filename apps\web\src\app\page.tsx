import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Zap, Car, MapPin, BarChart3, Leaf, Users } from 'lucide-react'

export default function HomePage() {
  return (
    <div className="flex flex-col min-h-screen">
      {/* Header */}
      <header className="border-b bg-white/95 backdrop-blur supports-[backdrop-filter]:bg-white/60">
        <div className="container flex h-16 items-center justify-between">
          <div className="flex items-center space-x-2">
            <Zap className="h-6 w-6 text-electric-600" />
            <span className="text-xl font-bold text-gray-900">GreenMilesEV</span>
          </div>
          <nav className="hidden md:flex items-center space-x-6">
            <a href="#features" className="text-sm font-medium hover:text-electric-600">Features</a>
            <a href="#about" className="text-sm font-medium hover:text-electric-600">About</a>
            <a href="#contact" className="text-sm font-medium hover:text-electric-600">Contact</a>
          </nav>
          <div className="flex items-center space-x-2">
            <Button variant="ghost" size="sm">Sign In</Button>
            <Button size="sm" className="bg-electric-600 hover:bg-electric-700">Get Started</Button>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="flex-1 flex items-center justify-center bg-gradient-to-br from-electric-50 to-green-50 py-20">
        <div className="container text-center space-y-8">
          <Badge variant="secondary" className="mb-4">
            <Leaf className="h-3 w-3 mr-1" />
            Sustainable Transportation
          </Badge>
          <h1 className="text-4xl md:text-6xl font-bold tracking-tight text-gray-900">
            Drive the Future with{' '}
            <span className="text-electric-600">Electric Vehicles</span>
          </h1>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Comprehensive platform for electric vehicle management, charging station discovery, 
            and sustainable transportation planning.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" className="bg-electric-600 hover:bg-electric-700">
              <Car className="h-4 w-4 mr-2" />
              Start Your Journey
            </Button>
            <Button size="lg" variant="outline">
              <MapPin className="h-4 w-4 mr-2" />
              Find Charging Stations
            </Button>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="py-20 bg-white">
        <div className="container">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Everything You Need for EV Management
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              From vehicle tracking to charging optimization, we provide all the tools 
              for a seamless electric vehicle experience.
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            <Card className="border-0 shadow-lg hover:shadow-xl transition-shadow">
              <CardHeader>
                <Car className="h-10 w-10 text-electric-600 mb-2" />
                <CardTitle>Vehicle Management</CardTitle>
                <CardDescription>
                  Track your EV's performance, maintenance, and efficiency metrics in real-time.
                </CardDescription>
              </CardHeader>
            </Card>

            <Card className="border-0 shadow-lg hover:shadow-xl transition-shadow">
              <CardHeader>
                <MapPin className="h-10 w-10 text-electric-600 mb-2" />
                <CardTitle>Charging Network</CardTitle>
                <CardDescription>
                  Find nearby charging stations with real-time availability and pricing information.
                </CardDescription>
              </CardHeader>
            </Card>

            <Card className="border-0 shadow-lg hover:shadow-xl transition-shadow">
              <CardHeader>
                <BarChart3 className="h-10 w-10 text-electric-600 mb-2" />
                <CardTitle>Analytics Dashboard</CardTitle>
                <CardDescription>
                  Monitor energy consumption, cost savings, and environmental impact.
                </CardDescription>
              </CardHeader>
            </Card>

            <Card className="border-0 shadow-lg hover:shadow-xl transition-shadow">
              <CardHeader>
                <Zap className="h-10 w-10 text-electric-600 mb-2" />
                <CardTitle>Smart Charging</CardTitle>
                <CardDescription>
                  Optimize charging schedules based on energy prices and grid demand.
                </CardDescription>
              </CardHeader>
            </Card>

            <Card className="border-0 shadow-lg hover:shadow-xl transition-shadow">
              <CardHeader>
                <Leaf className="h-10 w-10 text-electric-600 mb-2" />
                <CardTitle>Carbon Tracking</CardTitle>
                <CardDescription>
                  Track your carbon footprint reduction and environmental contributions.
                </CardDescription>
              </CardHeader>
            </Card>

            <Card className="border-0 shadow-lg hover:shadow-xl transition-shadow">
              <CardHeader>
                <Users className="h-10 w-10 text-electric-600 mb-2" />
                <CardTitle>Community</CardTitle>
                <CardDescription>
                  Connect with other EV owners and share experiences and tips.
                </CardDescription>
              </CardHeader>
            </Card>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="border-t bg-gray-50 py-12">
        <div className="container">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="flex items-center space-x-2 mb-4 md:mb-0">
              <Zap className="h-5 w-5 text-electric-600" />
              <span className="font-semibold text-gray-900">GreenMilesEV</span>
            </div>
            <p className="text-sm text-gray-600">
              © 2024 GreenMilesEV. All rights reserved. Building a sustainable future.
            </p>
          </div>
        </div>
      </footer>
    </div>
  )
}
